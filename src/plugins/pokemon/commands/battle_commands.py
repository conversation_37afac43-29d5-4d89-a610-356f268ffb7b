"""
Battle Commands

Nonebot commands for Pokemon battles.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from nonebot import on_command, get_driver
from nonebot.adapters.onebot.v11 import Bot, Event, MessageEvent, GroupMessageEvent
from nonebot.params import CommandArg, ArgStr
from nonebot.typing import T_State
from nonebot.message import run_preprocessor
from nonebot.exception import FinishedException

from ..models import BattleFormat, Team, PokemonSet, BattleStatus
from ..battle import BattleManager
from ..database import PokemonDatabase
from ..showdown_bridge import ShowdownBridge
from ..config import config

logger = logging.getLogger(__name__)

# Global instances (will be initialized in plugin init)
battle_manager: Optional[BattleManager] = None
pokemon_db: Optional[PokemonDatabase] = None
showdown_bridge: Optional[ShowdownBridge] = None

# Battle challenges storage
pending_challenges: Dict[str, Dict[str, Any]] = {}  # challenger_id -> challenge_data

# Command handlers
battle_challenge = on_command("pokemon battle", aliases={"pokemon挑战", "宝可梦对战"}, priority=5)
battle_accept = on_command("pokemon accept", aliases={"pokemon接受", "宝可梦接受"}, priority=5)
battle_action = on_command("pokemon action", aliases={"pokemon行动", "宝可梦行动"}, priority=5)
battle_forfeit = on_command("pokemon forfeit", aliases={"pokemon投降", "宝可梦投降"}, priority=5)
battle_status = on_command("pokemon status", aliases={"pokemon状态", "宝可梦状态"}, priority=5)


@battle_challenge.handle()
async def handle_battle_challenge(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle battle challenge command"""
    try:
        if not battle_manager or not battle_manager._initialized:
            await battle_challenge.finish("战斗系统尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await battle_challenge.finish(
                "使用方法: /pokemon battle <@用户> <格式> [队伍]\n"
                "格式: gen9ou, gen9uu, gen9randombattle 等\n"
                "示例: /pokemon battle @张三 gen9ou"
            )
        
        # Parse arguments
        parts = args_str.split()
        if len(parts) < 2:
            await battle_challenge.finish("参数不足，请指定对手和对战格式")
        
        # Extract target user (assuming @mention format)
        target_mention = parts[0]
        format_str = parts[1]
        
        # Validate format
        try:
            battle_format = BattleFormat(format_str.lower())
        except ValueError:
            await battle_challenge.finish(f"不支持的对战格式: {format_str}")
        
        challenger_id = str(event.user_id)
        
        # Check if challenger is already in a battle
        user_battles = await battle_manager.get_user_battles(challenger_id)
        if user_battles:
            await battle_challenge.finish("你已经在对战中，无法发起新的挑战")
        
        # For now, use random teams for simplicity
        # In a real implementation, you'd load user teams
        challenger_team = await _generate_random_team(battle_format)
        if not challenger_team:
            await battle_challenge.finish("生成队伍失败，请稍后再试")
        
        # Store challenge
        challenge_data = {
            "challenger_id": challenger_id,
            "challenger_name": event.sender.nickname or f"用户{challenger_id}",
            "target_mention": target_mention,
            "format": battle_format,
            "challenger_team": challenger_team,
            "timestamp": asyncio.get_event_loop().time()
        }
        
        pending_challenges[challenger_id] = challenge_data
        
        await battle_challenge.finish(
            f"已向 {target_mention} 发起 {battle_format.value} 格式的对战挑战！\n"
            f"对方可以使用 '/pokemon accept {challenger_id}' 接受挑战"
        )
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in battle challenge: {e}")
        await battle_challenge.finish("发起挑战时出现错误，请稍后再试")


@battle_accept.handle()
async def handle_battle_accept(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle battle accept command"""
    try:
        if not battle_manager or not battle_manager._initialized:
            await battle_accept.finish("战斗系统尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await battle_accept.finish("请指定要接受的挑战者ID")
        
        challenger_id = args_str
        accepter_id = str(event.user_id)
        
        # Check if challenge exists
        if challenger_id not in pending_challenges:
            await battle_accept.finish("未找到该挑战，可能已过期或不存在")
        
        challenge_data = pending_challenges[challenger_id]
        
        # Check if accepter is already in a battle
        user_battles = await battle_manager.get_user_battles(accepter_id)
        if user_battles:
            await battle_accept.finish("你已经在对战中，无法接受新的挑战")
        
        # Generate team for accepter
        accepter_team = await _generate_random_team(challenge_data["format"])
        if not accepter_team:
            await battle_accept.finish("生成队伍失败，请稍后再试")
        
        # Create battle
        battle_handler = await battle_manager.create_battle(
            format=challenge_data["format"],
            player1_team=challenge_data["challenger_team"],
            player1_username=challenge_data["challenger_name"],
            player1_user_id=challenger_id,
            player2_team=accepter_team,
            player2_username=event.sender.nickname or f"用户{accepter_id}",
            player2_user_id=accepter_id
        )
        
        if not battle_handler:
            await battle_accept.finish("创建对战失败，请稍后再试")
        
        # Remove challenge
        del pending_challenges[challenger_id]
        
        # Notify both players
        battle_id = battle_handler.battle.id
        await battle_accept.send(
            f"对战已开始！\n"
            f"对战ID: {battle_id}\n"
            f"格式: {challenge_data['format'].value}\n"
            f"挑战者: {challenge_data['challenger_name']}\n"
            f"接受者: {event.sender.nickname or f'用户{accepter_id}'}\n"
            f"使用 '/pokemon status {battle_id}' 查看对战状态"
        )
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in battle accept: {e}")
        await battle_accept.finish("接受挑战时出现错误，请稍后再试")


@battle_action.handle()
async def handle_battle_action(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle battle action command"""
    try:
        if not battle_manager or not battle_manager._initialized:
            await battle_action.finish("战斗系统尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await battle_action.finish(
                "使用方法: /pokemon action <对战ID> <行动>\n"
                "示例: /pokemon action abc123 move 1\n"
                "      /pokemon action abc123 switch 2"
            )
        
        parts = args_str.split(maxsplit=1)
        if len(parts) < 2:
            await battle_action.finish("参数不足，请指定对战ID和行动")
        
        battle_id = parts[0]
        action = parts[1]
        user_id = str(event.user_id)
        
        # Get battle
        battle_handler = await battle_manager.get_battle(battle_id)
        if not battle_handler:
            await battle_action.finish("未找到指定的对战")
        
        # Submit action
        success = await battle_manager.submit_battle_action(battle_id, user_id, action)
        
        if success:
            await battle_action.finish("行动已提交")
        else:
            await battle_action.finish("提交行动失败，请检查对战状态和行动格式")
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in battle action: {e}")
        await battle_action.finish("提交行动时出现错误，请稍后再试")


@battle_forfeit.handle()
async def handle_battle_forfeit(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle battle forfeit command"""
    try:
        if not battle_manager or not battle_manager._initialized:
            await battle_forfeit.finish("战斗系统尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        if not args_str:
            await battle_forfeit.finish("请指定要投降的对战ID")
        
        battle_id = args_str
        user_id = str(event.user_id)
        
        # Forfeit battle
        success = await battle_manager.forfeit_battle(battle_id, user_id)
        
        if success:
            await battle_forfeit.finish("已投降，对战结束")
        else:
            await battle_forfeit.finish("投降失败，请检查对战ID")
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in battle forfeit: {e}")
        await battle_forfeit.finish("投降时出现错误，请稍后再试")


@battle_status.handle()
async def handle_battle_status(bot: Bot, event: MessageEvent, state: T_State, args=CommandArg()):
    """Handle battle status command"""
    try:
        if not battle_manager or not battle_manager._initialized:
            await battle_status.finish("战斗系统尚未初始化，请稍后再试")
        
        args_str = str(args).strip()
        user_id = str(event.user_id)
        
        if args_str:
            # Get specific battle status
            battle_id = args_str
            battle_handler = await battle_manager.get_battle(battle_id)
            
            if not battle_handler:
                await battle_status.finish("未找到指定的对战")
            
            summary = battle_handler.get_battle_summary()
            status_text = _format_battle_status(summary)
            await battle_status.finish(status_text)
        else:
            # Get user's battles
            user_battles = await battle_manager.get_user_battles(user_id)
            
            if not user_battles:
                await battle_status.finish("你当前没有进行中的对战")
            
            status_lines = ["你的对战状态:"]
            for battle_handler in user_battles:
                summary = battle_handler.get_battle_summary()
                status_lines.append(f"- {summary['id']}: {summary['status']}")
            
            await battle_status.finish("\n".join(status_lines))
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error in battle status: {e}")
        await battle_status.finish("查询状态时出现错误，请稍后再试")


async def _generate_random_team(format: BattleFormat) -> Optional[Team]:
    """Generate a random team for the format"""
    try:
        if not pokemon_db or not pokemon_db._initialized:
            return None

        # Get random Pokemon
        random_pokemon = await pokemon_db.get_random_pokemon(6)
        if len(random_pokemon) < 6:
            return None

        # Create team
        pokemon_sets = []
        for pokemon in random_pokemon:
            # Get random moves - use filters to exclude Z-moves and other problematic moves
            filters = {
                "category": "Physical",  # Start with physical moves
                "min_power": 40,
                "max_power": 120
            }
            physical_moves = await pokemon_db.search_moves("", limit=2, filters=filters)

            filters["category"] = "Special"  # Then special moves
            special_moves = await pokemon_db.search_moves("", limit=1, filters=filters)

            filters["category"] = "Status"  # Finally status moves
            filters.pop("min_power", None)
            filters.pop("max_power", None)
            status_moves = await pokemon_db.search_moves("", limit=1, filters=filters)

            # Combine moves and ensure we have 4
            all_moves = physical_moves + special_moves + status_moves
            if len(all_moves) < 4:
                # Fallback: get some basic moves
                basic_moves = ["Tackle", "Quick Attack", "Rest", "Protect"]
                move_names = basic_moves[:4]
            else:
                move_names = [move.name for move in all_moves[:4]]

            pokemon_set = PokemonSet(
                name=pokemon.name,
                species=pokemon.name,
                moves=move_names,
                level=50 if format != BattleFormat.GEN9LC else 5
            )
            pokemon_sets.append(pokemon_set)

        team = Team(
            name=f"Random {format.value} Team",
            pokemon=pokemon_sets,
            format=format,
            owner_id="system"
        )

        return team
        
    except FinishedException:
        raise
    except Exception as e:
        logger.error(f"Error generating random team: {e}")
        return None


def _format_battle_status(summary: Dict[str, Any]) -> str:
    """Format battle status for display"""
    lines = [
        f"对战ID: {summary['id']}",
        f"格式: {summary['format']}",
        f"状态: {summary['status']}",
        f"回合: {summary['current_turn']}",
        f"玩家:"
    ]
    
    for player_id, player_info in summary['players'].items():
        lines.append(f"  {player_id}: {player_info['username']}")
    
    if summary['winner']:
        lines.append(f"获胜者: {summary['winner']}")
    
    return "\n".join(lines)


# Initialize global instances when plugin loads
@get_driver().on_startup
async def init_battle_commands():
    """Initialize battle commands"""
    global battle_manager, pokemon_db, showdown_bridge
    
    # Import from main plugin
    from .. import battle_manager as bm, pokemon_db as pdb, showdown_bridge as sb
    
    battle_manager = bm
    pokemon_db = pdb
    showdown_bridge = sb
