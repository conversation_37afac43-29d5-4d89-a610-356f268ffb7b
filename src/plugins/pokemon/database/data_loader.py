"""
Pokemon Showdown Data Loader

Loads and parses data from Pokemon Showdown's data files.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, Optional, Any
import asyncio

from ..config import config
from ..models import Pokemon, Move, Item, Ability, PokemonStat, PokemonType

logger = logging.getLogger(__name__)


class ShowdownDataLoader:
    """Loads data from Pokemon Showdown data files"""
    
    def __init__(self):
        self.showdown_path = Path(config.showdown_path)
        self.data_path = self.showdown_path / "data"
        self.dist_path = self.showdown_path / "dist" / "data"
        self._cache: Dict[str, Any] = {}
    
    async def load_all_data(self) -> Dict[str, Any]:
        """Load all Pokemon data"""
        try:
            logger.info("Loading Pokemon data from Showdown...")
            
            data = {}
            
            # Load different data types
            data["pokemon"] = await self._load_pokemon_data()
            data["moves"] = await self._load_moves_data()
            data["items"] = await self._load_items_data()
            data["abilities"] = await self._load_abilities_data()
            data["natures"] = await self._load_natures_data()
            data["types"] = await self._load_types_data()
            
            logger.info(f"Loaded {len(data['pokemon'])} Pokemon, {len(data['moves'])} moves, "
                       f"{len(data['items'])} items, {len(data['abilities'])} abilities")
            
            return data
            
        except Exception as e:
            logger.error(f"Error loading Pokemon data: {e}")
            return {}
    
    async def _load_pokemon_data(self) -> Dict[str, Pokemon]:
        """Load Pokemon species data"""
        try:
            # Try to load from compiled JS first, then fall back to source
            pokedex_data = await self._load_js_data("pokedex.js")
            if not pokedex_data:
                logger.warning("Could not load compiled pokedex data, trying source files")
                return {}
            
            pokemon_dict = {}
            
            for pokemon_id, data in pokedex_data.items():
                try:
                    # Convert types
                    types = [PokemonType(t) for t in data.get("types", [])]
                    
                    # Convert base stats
                    base_stats_data = data.get("baseStats", {})
                    base_stats = PokemonStat(
                        hp=base_stats_data.get("hp", 0),
                        attack=base_stats_data.get("atk", 0),
                        defense=base_stats_data.get("def", 0),
                        special_attack=base_stats_data.get("spa", 0),
                        special_defense=base_stats_data.get("spd", 0),
                        speed=base_stats_data.get("spe", 0)
                    )
                    
                    # Convert abilities
                    abilities_data = data.get("abilities", {})
                    abilities = {}
                    for slot, ability in abilities_data.items():
                        abilities[str(slot)] = ability
                    
                    pokemon = Pokemon(
                        id=pokemon_id,
                        name=data.get("name", pokemon_id),
                        num=data.get("num", 0),
                        types=types,
                        base_stats=base_stats,
                        abilities=abilities,
                        height=data.get("heightm", 0.0),
                        weight=data.get("weightkg", 0.0),
                        color=data.get("color", ""),
                        egg_groups=data.get("eggGroups", []),
                        tier=data.get("tier"),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    pokemon_dict[pokemon_id] = pokemon
                    
                except Exception as e:
                    logger.warning(f"Error parsing Pokemon {pokemon_id}: {e}")
                    continue
            
            return pokemon_dict
            
        except Exception as e:
            logger.error(f"Error loading Pokemon data: {e}")
            return {}
    
    async def _load_moves_data(self) -> Dict[str, Move]:
        """Load moves data"""
        try:
            moves_data = await self._load_js_data("moves.js")
            if not moves_data:
                return {}
            
            moves_dict = {}
            
            for move_id, data in moves_data.items():
                try:
                    move = Move(
                        id=move_id,
                        name=data.get("name", move_id),
                        num=data.get("num", 0),
                        type=PokemonType(data.get("type", "Normal")),
                        category=data.get("category", "Status"),
                        base_power=data.get("basePower", 0),
                        accuracy=data.get("accuracy", True),
                        pp=data.get("pp", 0),
                        priority=data.get("priority", 0),
                        target=data.get("target", ""),
                        description=data.get("desc", ""),
                        short_description=data.get("shortDesc", ""),
                        flags=data.get("flags", {}),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    moves_dict[move_id] = move
                    
                except Exception as e:
                    logger.warning(f"Error parsing move {move_id}: {e}")
                    continue
            
            return moves_dict
            
        except Exception as e:
            logger.error(f"Error loading moves data: {e}")
            return {}
    
    async def _load_items_data(self) -> Dict[str, Item]:
        """Load items data"""
        try:
            items_data = await self._load_js_data("items.js")
            if not items_data:
                return {}
            
            items_dict = {}
            
            for item_id, data in items_data.items():
                try:
                    item = Item(
                        id=item_id,
                        name=data.get("name", item_id),
                        num=data.get("num", 0),
                        description=data.get("desc", ""),
                        short_description=data.get("shortDesc", ""),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    items_dict[item_id] = item
                    
                except Exception as e:
                    logger.warning(f"Error parsing item {item_id}: {e}")
                    continue
            
            return items_dict
            
        except Exception as e:
            logger.error(f"Error loading items data: {e}")
            return {}
    
    async def _load_abilities_data(self) -> Dict[str, Ability]:
        """Load abilities data"""
        try:
            abilities_data = await self._load_js_data("abilities.js")
            if not abilities_data:
                return {}
            
            abilities_dict = {}
            
            for ability_id, data in abilities_data.items():
                try:
                    ability = Ability(
                        id=ability_id,
                        name=data.get("name", ability_id),
                        num=data.get("num", 0),
                        description=data.get("desc", ""),
                        short_description=data.get("shortDesc", ""),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    abilities_dict[ability_id] = ability
                    
                except Exception as e:
                    logger.warning(f"Error parsing ability {ability_id}: {e}")
                    continue
            
            return abilities_dict
            
        except Exception as e:
            logger.error(f"Error loading abilities data: {e}")
            return {}
    
    async def _load_natures_data(self) -> Dict[str, Dict[str, Any]]:
        """Load natures data"""
        try:
            natures_data = await self._load_js_data("natures.js")
            return natures_data or {}
            
        except Exception as e:
            logger.error(f"Error loading natures data: {e}")
            return {}
    
    async def _load_types_data(self) -> Dict[str, Dict[str, Any]]:
        """Load type chart data"""
        try:
            types_data = await self._load_js_data("typechart.js")
            return types_data or {}
            
        except Exception as e:
            logger.error(f"Error loading types data: {e}")
            return {}
    
    async def _load_js_data(self, filename: str) -> Optional[Dict[str, Any]]:
        """Load data from a compiled JavaScript file using Node.js"""
        try:
            file_path = self.dist_path / filename

            if not file_path.exists():
                logger.warning(f"Data file not found: {file_path}")
                return None

            # Use Node.js to evaluate the JavaScript and output JSON
            data = await self._execute_js_to_json(file_path)

            return data

        except Exception as e:
            logger.error(f"Error loading JS data from {filename}: {e}")
            return None
    

    
    async def _execute_js_to_json(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Execute JavaScript file using Node.js and extract data as JSON"""
        try:
            # Convert to absolute path to avoid path resolution issues
            absolute_file_path = file_path.resolve()

            # Create a temporary script to load the JS file and output JSON
            temp_script = f"""
const path = require('path');
const fs = require('fs');

// Load the data file using absolute path
const dataPath = '{absolute_file_path}';
delete require.cache[require.resolve(dataPath)];
const data = require(dataPath);

// Find the main data object
let mainData = null;
if (data.Pokedex) mainData = data.Pokedex;
else if (data.Moves) mainData = data.Moves;
else if (data.Items) mainData = data.Items;
else if (data.Abilities) mainData = data.Abilities;
else if (data.Natures) mainData = data.Natures;
else if (data.TypeChart) mainData = data.TypeChart;
else {{
    // Try to find any exported object
    const keys = Object.keys(data);
    if (keys.length > 0) {{
        mainData = data[keys[0]];
    }}
}}

if (mainData) {{
    console.log(JSON.stringify(mainData, null, 0));
}} else {{
    console.error('No data found in file');
    process.exit(1);
}}
"""

            # Write temporary script
            temp_script_path = self.showdown_path / "temp_extract.js"
            temp_script_path.write_text(temp_script)

            try:
                # Execute the script with Node.js
                process = await asyncio.create_subprocess_exec(
                    'node', str(temp_script_path),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    # Parse the JSON output
                    json_data = json.loads(stdout.decode('utf-8'))
                    return json_data
                else:
                    logger.error(f"Node.js execution failed: {stderr.decode('utf-8')}")
                    return None

            finally:
                # Clean up temporary script
                if temp_script_path.exists():
                    temp_script_path.unlink()

        except Exception as e:
            logger.error(f"Error executing JS to JSON: {e}")
            return None
