"""
Pokemon Database

Main database interface for accessing Pokemon data.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from .data_loader import ShowdownDataLoader
from .cache_manager import DataCacheManager
from .translation_manager import translation_manager
from ..models import Pokemon, Move, Item, Ability, PokemonType, BattleFormat
from ..config import config

logger = logging.getLogger(__name__)


class PokemonDatabase:
    """Main Pokemon database interface"""
    
    def __init__(self):
        self.data_loader = ShowdownDataLoader()
        self.cache_manager = DataCacheManager()
        self._initialized = False
        self._data: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> bool:
        """Initialize the Pokemon database"""
        if self._initialized:
            return True
        
        async with self._lock:
            try:
                logger.info("Initializing Pokemon database...")
                
                # Try to load from cache first
                cached_data = await self.cache_manager.get("all_pokemon_data")
                
                if cached_data and config.cache_pokemon_data:
                    logger.info("Loading Pokemon data from cache...")
                    self._data = cached_data
                else:
                    logger.info("Loading Pokemon data from Showdown files...")
                    self._data = await self.data_loader.load_all_data()
                    
                    if self._data and config.cache_pokemon_data:
                        await self.cache_manager.set("all_pokemon_data", self._data)
                
                if not self._data:
                    logger.error("Failed to load Pokemon data")
                    return False
                
                # Preload common data
                await self.cache_manager.preload_common_data(self.data_loader)

                # Initialize translation manager
                await translation_manager.initialize()

                self._initialized = True
                logger.info("Pokemon database initialized successfully")
                return True
                
            except Exception as e:
                logger.error(f"Error initializing Pokemon database: {e}")
                return False
    
    async def get_pokemon(self, pokemon_id: str) -> Optional[Pokemon]:
        """Get a Pokemon by ID or Chinese name"""
        if not self._initialized:
            await self.initialize()

        try:
            # Try to translate Chinese name to English first
            english_name = translation_manager.translate_to_english(pokemon_id)
            if english_name:
                pokemon_id = english_name

            # Normalize ID
            pokemon_id = pokemon_id.lower().replace(" ", "").replace("-", "")

            # Check cache first
            cached = await self.cache_manager.get(f"pokemon_{pokemon_id}")
            if cached:
                return cached

            # Get from main data
            pokemon_data = self._data.get("pokemon", {})
            pokemon = pokemon_data.get(pokemon_id)

            if pokemon:
                # Cache for future use
                await self.cache_manager.set(f"pokemon_{pokemon_id}", pokemon, persist=False)

            return pokemon

        except Exception as e:
            logger.error(f"Error getting Pokemon {pokemon_id}: {e}")
            return None
    
    async def get_move(self, move_id: str) -> Optional[Move]:
        """Get a move by ID or Chinese name"""
        if not self._initialized:
            await self.initialize()

        try:
            # Try to translate Chinese name to English first
            english_name = translation_manager.translate_to_english(move_id)
            if english_name:
                move_id = english_name

            # Normalize ID
            move_id = move_id.lower().replace(" ", "").replace("-", "")

            # Check cache first
            cached = await self.cache_manager.get(f"move_{move_id}")
            if cached:
                return cached

            # Get from main data
            moves_data = self._data.get("moves", {})
            move = moves_data.get(move_id)

            if move:
                # Cache for future use
                await self.cache_manager.set(f"move_{move_id}", move, persist=False)

            return move

        except Exception as e:
            logger.error(f"Error getting move {move_id}: {e}")
            return None
    
    async def get_item(self, item_id: str) -> Optional[Item]:
        """Get an item by ID or Chinese name"""
        if not self._initialized:
            await self.initialize()

        try:
            # Try to translate Chinese name to English first
            english_name = translation_manager.translate_to_english(item_id)
            if english_name:
                item_id = english_name

            # Normalize ID
            item_id = item_id.lower().replace(" ", "").replace("-", "")

            # Check cache first
            cached = await self.cache_manager.get(f"item_{item_id}")
            if cached:
                return cached

            # Get from main data
            items_data = self._data.get("items", {})
            item = items_data.get(item_id)

            if item:
                # Cache for future use
                await self.cache_manager.set(f"item_{item_id}", item, persist=False)

            return item

        except Exception as e:
            logger.error(f"Error getting item {item_id}: {e}")
            return None
    
    async def get_ability(self, ability_id: str) -> Optional[Ability]:
        """Get an ability by ID or Chinese name"""
        if not self._initialized:
            await self.initialize()

        try:
            # Try to translate Chinese name to English first
            english_name = translation_manager.translate_to_english(ability_id)
            if english_name:
                ability_id = english_name

            # Normalize ID
            ability_id = ability_id.lower().replace(" ", "").replace("-", "")

            # Check cache first
            cached = await self.cache_manager.get(f"ability_{ability_id}")
            if cached:
                return cached

            # Get from main data
            abilities_data = self._data.get("abilities", {})
            ability = abilities_data.get(ability_id)

            if ability:
                # Cache for future use
                await self.cache_manager.set(f"ability_{ability_id}", ability, persist=False)

            return ability

        except Exception as e:
            logger.error(f"Error getting ability {ability_id}: {e}")
            return None
    
    async def search_pokemon(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Pokemon]:
        """Search for Pokemon by name or other criteria (supports Chinese names)"""
        if not self._initialized:
            await self.initialize()

        try:
            query_lower = query.lower()
            results = []
            pokemon_data = self._data.get("pokemon", {})

            # Try to translate Chinese query to English
            english_query = translation_manager.translate_to_english(query)

            for pokemon_id, pokemon in pokemon_data.items():
                match_found = False

                # Check if query matches English name or ID
                if (query_lower in pokemon.name.lower() or
                    query_lower in pokemon_id.lower()):
                    match_found = True

                # Check if translated query matches
                if english_query and (english_query.lower() in pokemon.name.lower() or
                                    english_query.lower() in pokemon_id.lower()):
                    match_found = True

                # Check if query matches Chinese name
                chinese_name = translation_manager.translate_to_chinese(pokemon.name)
                if chinese_name and query in chinese_name:
                    match_found = True

                if match_found:
                    # Apply filters if provided
                    if filters:
                        if not self._apply_pokemon_filters(pokemon, filters):
                            continue

                    results.append(pokemon)

                    if len(results) >= limit:
                        break

            return results
            
        except Exception as e:
            logger.error(f"Error searching Pokemon: {e}")
            return []
    
    async def search_moves(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Move]:
        """Search for moves by name or other criteria (supports Chinese names)"""
        if not self._initialized:
            await self.initialize()

        try:
            query_lower = query.lower().strip()
            results = []
            moves_data = self._data.get("moves", {})

            # Try to translate Chinese query to English
            english_query = translation_manager.translate_to_english(query) if query.strip() else None

            # If query is empty, return filtered moves or common moves
            if not query_lower:
                # When no query provided, return common/basic moves first
                common_moves = [
                    "tackle", "quickattack", "thunderbolt", "flamethrower", "surf", "earthquake",
                    "psychic", "shadowball", "airslash", "energyball", "icebeam", "rockslide",
                    "protect", "rest", "substitute", "toxic", "stealthrock", "recover"
                ]

                # First try to get common moves
                for move_name in common_moves:
                    if move_name in moves_data:
                        move = moves_data[move_name]
                        if filters and not self._apply_move_filters(move, filters):
                            continue
                        results.append(move)
                        if len(results) >= limit:
                            break

                # If we still need more moves, add others that pass filters
                if len(results) < limit:
                    for move_id, move in moves_data.items():
                        if move in results:
                            continue

                        # Skip Z-moves and other problematic moves
                        if any(keyword in move.name.lower() for keyword in [
                            "10,000,000", "z-", "max ", "g-max", "dynamax"
                        ]):
                            continue

                        if filters and not self._apply_move_filters(move, filters):
                            continue

                        results.append(move)
                        if len(results) >= limit:
                            break

                return results

            for move_id, move in moves_data.items():
                match_found = False

                # Check if query matches English name or ID
                if (query_lower in move.name.lower() or
                    query_lower in move_id.lower()):
                    match_found = True

                # Check if translated query matches
                if english_query and (english_query.lower() in move.name.lower() or
                                    english_query.lower() in move_id.lower()):
                    match_found = True

                # Check if query matches Chinese name
                chinese_name = translation_manager.translate_to_chinese(move.name)
                if chinese_name and query in chinese_name:
                    match_found = True

                if match_found:
                    # Apply filters if provided
                    if filters:
                        if not self._apply_move_filters(move, filters):
                            continue

                    results.append(move)

                    if len(results) >= limit:
                        break

            return results

        except Exception as e:
            logger.error(f"Error searching moves: {e}")
            return []
    
    def _apply_pokemon_filters(self, pokemon: Pokemon, filters: Dict[str, Any]) -> bool:
        """Apply filters to Pokemon search"""
        try:
            # Type filter
            if "type" in filters:
                filter_type = filters["type"]
                if isinstance(filter_type, str):
                    filter_type = [filter_type]
                
                if not any(t.value in filter_type for t in pokemon.types):
                    return False
            
            # Tier filter
            if "tier" in filters:
                if pokemon.tier != filters["tier"]:
                    return False
            
            # Generation filter (based on Pokemon number)
            if "generation" in filters:
                gen = filters["generation"]
                gen_ranges = {
                    1: (1, 151), 2: (152, 251), 3: (252, 386),
                    4: (387, 493), 5: (494, 649), 6: (650, 721),
                    7: (722, 809), 8: (810, 905), 9: (906, 1025)
                }
                
                if gen in gen_ranges:
                    start, end = gen_ranges[gen]
                    if not (start <= pokemon.num <= end):
                        return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Error applying Pokemon filters: {e}")
            return True
    
    def _apply_move_filters(self, move: Move, filters: Dict[str, Any]) -> bool:
        """Apply filters to move search"""
        try:
            # Type filter
            if "type" in filters:
                if move.type.value != filters["type"]:
                    return False
            
            # Category filter
            if "category" in filters:
                if move.category != filters["category"]:
                    return False
            
            # Power filter
            if "min_power" in filters:
                if move.base_power < filters["min_power"]:
                    return False
            
            if "max_power" in filters:
                if move.base_power > filters["max_power"]:
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Error applying move filters: {e}")
            return True
    
    async def get_pokemon_by_type(self, pokemon_type: Union[str, PokemonType]) -> List[Pokemon]:
        """Get all Pokemon of a specific type"""
        if isinstance(pokemon_type, str):
            pokemon_type = PokemonType(pokemon_type)
        
        return await self.search_pokemon("", limit=1000, filters={"type": pokemon_type.value})
    
    async def get_moves_by_type(self, move_type: Union[str, PokemonType]) -> List[Move]:
        """Get all moves of a specific type"""
        if isinstance(move_type, str):
            move_type = PokemonType(move_type)
        
        return await self.search_moves("", limit=1000, filters={"type": move_type.value})
    
    async def get_random_pokemon(self, count: int = 1, filters: Optional[Dict[str, Any]] = None) -> List[Pokemon]:
        """Get random Pokemon"""
        import random
        
        if not self._initialized:
            await self.initialize()
        
        try:
            pokemon_data = self._data.get("pokemon", {})
            all_pokemon = list(pokemon_data.values())
            
            # Apply filters if provided
            if filters:
                filtered_pokemon = []
                for pokemon in all_pokemon:
                    if self._apply_pokemon_filters(pokemon, filters):
                        filtered_pokemon.append(pokemon)
                all_pokemon = filtered_pokemon
            
            # Return random selection
            if len(all_pokemon) <= count:
                return all_pokemon
            
            return random.sample(all_pokemon, count)
            
        except Exception as e:
            logger.error(f"Error getting random Pokemon: {e}")
            return []
    
    async def refresh_data(self) -> bool:
        """Refresh Pokemon data from source"""
        try:
            logger.info("Refreshing Pokemon data...")
            
            # Clear cache
            await self.cache_manager.clear()
            
            # Reload data
            self._data = await self.data_loader.load_all_data()
            
            if self._data and config.cache_pokemon_data:
                await self.cache_manager.set("all_pokemon_data", self._data)
            
            logger.info("Pokemon data refreshed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error refreshing Pokemon data: {e}")
            return False
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            stats = {
                "initialized": self._initialized,
                "pokemon_count": len(self._data.get("pokemon", {})),
                "moves_count": len(self._data.get("moves", {})),
                "items_count": len(self._data.get("items", {})),
                "abilities_count": len(self._data.get("abilities", {})),
                "cache_info": await self.cache_manager.get_cache_info()
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {"error": str(e)}
